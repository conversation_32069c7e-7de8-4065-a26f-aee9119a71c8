//go:build windows

package main

import (
	"os"

	"github.com/getlantern/systray"
)

func startWithSystray() {
	// 先启动主进程
	go VPNCrawlerMain()

	// 启动系统托盘
	systray.Run(onReady, onExit)
}

func onReady() {
	// 创建一个简单的16x16像素的ICO图标数据
	iconData := []byte{}

	// 尝试加载自定义图标，如果失败就使用默认图标
	if customIcon, err := StaticFS.ReadFile("static/favicon.ico"); err == nil && len(customIcon) > 0 {
		iconData = customIcon
	}

	// 设置系统托盘图标和属性
	systray.SetIcon(iconData)
	systray.SetTitle("VPNCrawler")
	systray.SetTooltip("VPNCrawler") // 托盘中显示的名称

	// 添加菜单项
	// mOpen := systray.AddMenuItem("打开主界面", "打开 VPNCrawler 主界面")

	systray.AddSeparator()

	mQuit := systray.AddMenuItem("退出", "退出应用程序")

	// 处理菜单点击事件
	go func() {
		for {
			select {
			// case <-mOpen.ClickedCh:
			// 	openMainWindow()

			case <-mQuit.ClickedCh:
				systray.Quit()
				return
			}
		}
	}()
}

func onExit() {
	// 退出主进程
	os.Exit(0)
}

// 打开主窗口
// func openMainWindow() {
// 	// 从config.Conf.Addr 中提取端口号
// 	addr := config.Conf.Addr
// 	port := strings.Split(addr, ":")[1]
// 	url := "http://127.0.0.1:" + port
// 	var cmd *exec.Cmd
// 	switch runtime.GOOS {
// 	case "windows":
// 		cmd = exec.Command("cmd", "/c", "start", url)
// 		// 隐藏命令行窗口
// 		cmd.SysProcAttr = &syscall.SysProcAttr{HideWindow: true}
// 	case "darwin":
// 		cmd = exec.Command("open", url)
// 	default:
// 		cmd = exec.Command("xdg-open", url)
// 	}
// 	cmd.Start()
// }
