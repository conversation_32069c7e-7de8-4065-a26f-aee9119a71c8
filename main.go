package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"regexp"
	"strings"
	"time"

	"github.com/gen2brain/beeep"
)

// VPNInfo 试用机场信息结构体
type VPNInfo struct {
	Name          string    `json:"name"`           // 机场名称
	Type          string    `json:"type"`           // 机场类型（试用机场、免费机场等）
	CouponCode    string    `json:"coupon_code"`    // 优惠券代码或获取方式
	RegisterURL   string    `json:"register_url"`   // 注册地址
	UpdateDate    string    `json:"update_date"`    // 更新日期
	Locations     string    `json:"locations"`      // 节点位置
	Protocol      string    `json:"protocol"`       // 协议类型
	NodeCount     string    `json:"node_count"`     // 节点数量
	OfficialGroup string    `json:"official_group"` // 官方群组/频道
	Package       string    `json:"package"`        // 套餐信息
	AddedTime     time.Time `json:"added_time"`     // 添加时间
}

// DingTalkMessage 钉钉消息结构体
type DingTalkMessage struct {
	MsgType  string           `json:"msgtype"`
	Markdown DingTalkMarkdown `json:"markdown"`
}

type DingTalkMarkdown struct {
	Title string `json:"title"`
	Text  string `json:"text"`
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	EnableDingTalk bool   `json:"enable_dingtalk"` // 是否启用钉钉通知
	EnableDesktop  bool   `json:"enable_desktop"`  // 是否启用桌面通知
	EnableFile     bool   `json:"enable_file"`     // 是否启用文件通知
	DingTalkURL    string `json:"dingtalk_url"`    // 钉钉机器人URL
	DingTalkSecret string `json:"dingtalk_secret"` // 钉钉机器人密钥
}

// VPNCrawler 爬虫结构体
type VPNCrawler struct {
	DataFile     string
	NotifyConfig NotificationConfig
	VPNData      []VPNInfo
}

// NewVPNCrawler 创建新的爬虫实例
func NewVPNCrawler(dataFile string, notifyConfig NotificationConfig) *VPNCrawler {
	crawler := &VPNCrawler{
		DataFile:     dataFile,
		NotifyConfig: notifyConfig,
		VPNData:      []VPNInfo{},
	}
	crawler.loadData()
	return crawler
}

// loadData 加载已存储的数据
func (c *VPNCrawler) loadData() {
	if _, err := os.Stat(c.DataFile); os.IsNotExist(err) {
		return
	}

	data, err := os.ReadFile(c.DataFile)
	if err != nil {
		fmt.Printf("读取数据文件失败: %v\n", err)
		return
	}

	if err := json.Unmarshal(data, &c.VPNData); err != nil {
		fmt.Printf("解析数据文件失败: %v\n", err)
	}
}

// saveData 保存数据到文件
func (c *VPNCrawler) saveData() error {
	data, err := json.MarshalIndent(c.VPNData, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化数据失败: %v", err)
	}

	return os.WriteFile(c.DataFile, data, 0644)
}

// fetchWebPage 获取网页内容
func (c *VPNCrawler) fetchWebPage() (string, error) {
	now := time.Now()
	url := fmt.Sprintf("https://ygpy.net/vpn/%d/%02d.html", now.Year(), int(now.Month()))

	fmt.Printf("正在抓取URL: %s\n", url)

	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	return string(body), nil
}

// extractCouponCode 提取优惠券代码或获取方式（优化版本）
func (c *VPNCrawler) extractCouponCode(sectionHTML string) string {
	// 方法1: 提取 <code> 标签中的优惠券代码
	couponPatterns := []string{
		`使用(?:优惠券|礼品卡|代码)\s*<code>([^<]+)</code>`,
		`优惠券(?:代码)?[：:]\s*<code>([^<]+)</code>`,
		`礼品卡(?:代码)?[：:]\s*<code>([^<]+)</code>`,
		`代码[：:]\s*<code>([^<]+)</code>`,
		`<code>([A-Z0-9]{4,})</code>`, // 匹配纯大写字母数字组合的代码
	}

	for _, pattern := range couponPatterns {
		couponRegex := regexp.MustCompile(pattern)
		if couponMatch := couponRegex.FindStringSubmatch(sectionHTML); len(couponMatch) > 1 {
			code := strings.TrimSpace(couponMatch[1])
			if code != "" {
				return code
			}
		}
	}

	// 方法2: 提取套餐获取方式
	packagePatterns := []string{
		`注册即送([^。<\n\r]+?)(?:套餐|流量|天)`,
		`免费获得([^。<\n\r]+?)(?:套餐|流量|天)`,
		`赠送([^。<\n\r]+?)(?:套餐|流量|天)`,
		`([0-9]+)\s*元购买[^。<\n\r]*?(?:套餐|流量)`,
		`新用户注册送([^。<\n\r]+?)(?:套餐|流量|天)`,
		`首次注册送([^。<\n\r]+?)(?:套餐|流量|天)`,
	}

	for _, pattern := range packagePatterns {
		packageRegex := regexp.MustCompile(pattern)
		if packageMatch := packageRegex.FindStringSubmatch(sectionHTML); len(packageMatch) > 1 {
			if strings.Contains(packageMatch[0], "0 元购买") ||
				strings.Contains(packageMatch[0], "注册即送") ||
				strings.Contains(packageMatch[0], "免费获得") ||
				strings.Contains(packageMatch[0], "赠送") {
				return "免费获取"
			} else {
				return "需购买"
			}
		}
	}

	// 方法3: 查找节点位置上一个<p>标签的内容
	// 先找到节点位置的位置
	locationIndex := strings.Index(sectionHTML, "节点位置：")
	if locationIndex != -1 {
		// 在节点位置之前查找<p>标签
		beforeLocation := sectionHTML[:locationIndex]

		// 从后往前查找最近的<p>标签内容
		pTagRegex := regexp.MustCompile(`<p[^>]*>([^<]*(?:<(?!/?p)[^<]*)*)</p>`)
		pMatches := pTagRegex.FindAllStringSubmatch(beforeLocation, -1)

		if len(pMatches) > 0 {
			// 取最后一个<p>标签的内容（最接近节点位置的）
			lastPContent := strings.TrimSpace(pMatches[len(pMatches)-1][1])

			// 检查是否包含优惠券相关信息
			couponKeywords := []string{"优惠券", "礼品卡", "代码", "免费", "试用", "注册送", "赠送"}
			for _, keyword := range couponKeywords {
				if strings.Contains(lastPContent, keyword) {
					// 清理HTML标签和多余空白
					cleanContent := regexp.MustCompile(`<[^>]*>`).ReplaceAllString(lastPContent, "")
					cleanContent = regexp.MustCompile(`\s+`).ReplaceAllString(cleanContent, " ")
					cleanContent = strings.TrimSpace(cleanContent)

					if len(cleanContent) > 0 && len(cleanContent) < 100 { // 避免过长的内容
						return cleanContent
					}
				}
			}
		}
	}

	// 方法4: 通用文本模式匹配
	generalPatterns := []string{
		`免费注册即可使用`,
		`注册即可免费试用`,
		`新用户免费试用`,
		`免费试用\d+天`,
		`试用期\d+天`,
		`无需优惠券`,
		`直接注册使用`,
	}

	for _, pattern := range generalPatterns {
		generalRegex := regexp.MustCompile(pattern)
		if generalRegex.MatchString(sectionHTML) {
			return "免费试用"
		}
	}

	// 方法5: 根据机场类型设置默认值
	if strings.Contains(sectionHTML, "免费机场") {
		return "免费注册"
	} else if strings.Contains(sectionHTML, "试用机场") {
		return "试用注册"
	}

	// 如果都没找到，返回空字符串，后续会设置默认值
	return ""
}

// parseTrialVPNs 解析试用机场信息
func (c *VPNCrawler) parseTrialVPNs(html string) []VPNInfo {
	var vpnList []VPNInfo

	// 使用更通用的正则表达式匹配免费/试用机场的h2标签
	// 支持多种Badge类型：试用机场、免费机场等
	trialRegex := regexp.MustCompile(`<h2[^>]*>\s*([^<]+?)\s*<span[^>]*VPBadge\s+(?:info|tip)[^>]*>\s*<!--\[-->\s*(试用机场|免费机场)\s*<!--\]-->\s*</span>[^<]*<a[^>]*>`)
	trialMatches := trialRegex.FindAllStringSubmatch(html, -1)

	fmt.Printf("找到 %d 个免费/试用机场标题\n", len(trialMatches))

	for i, match := range trialMatches {
		if len(match) < 3 {
			continue
		}

		vpnName := strings.TrimSpace(match[1])
		vpnType := strings.TrimSpace(match[2])
		fmt.Printf("正在解析第 %d 个机场: %s (%s)\n", i+1, vpnName, vpnType)

		// 找到这个机场对应的详细信息区域
		nameIndex := strings.Index(html, match[0])
		if nameIndex == -1 {
			fmt.Printf("未找到机场 %s 的位置索引\n", vpnName)
			continue
		}

		// 查找下一个h2标签的位置作为结束位置
		nextH2Index := strings.Index(html[nameIndex+len(match[0]):], "<h2")
		var sectionHTML string
		if nextH2Index == -1 {
			sectionHTML = html[nameIndex:]
		} else {
			sectionHTML = html[nameIndex : nameIndex+len(match[0])+nextH2Index]
		}

		vpn := VPNInfo{
			Name:      vpnName,
			Type:      vpnType,
			AddedTime: time.Now(),
		}

		// 提取优惠券代码或获取方式（优化版本）
		vpn.CouponCode = c.extractCouponCode(sectionHTML)

		// 如果仍然没有找到获取方式，根据类型设置默认值
		if vpn.CouponCode == "" {
			if vpn.Type == "免费机场" {
				vpn.CouponCode = "免费注册"
			} else {
				vpn.CouponCode = "试用注册"
			}
		}

		// 提取注册地址（支持中文域名）
		registerPatterns := []string{
			`注册地址：<a[^>]*href="([^"]+)"[^>]*>([^<]+)</a>`,
			`地址：<a[^>]*href="([^"]+)"[^>]*>([^<]+)</a>`,
		}

		for _, pattern := range registerPatterns {
			registerRegex := regexp.MustCompile(pattern)
			if registerMatch := registerRegex.FindStringSubmatch(sectionHTML); len(registerMatch) > 1 {
				vpn.RegisterURL = strings.TrimSpace(registerMatch[1])
				break
			}
		}

		// 提取节点位置
		locationRegex := regexp.MustCompile(`节点位置：([^<\n\r]+?)(?:</p>|<br)`)
		if locationMatch := locationRegex.FindStringSubmatch(sectionHTML); len(locationMatch) > 1 {
			vpn.Locations = strings.TrimSpace(locationMatch[1])
		}

		// 提取协议类型
		protocolRegex := regexp.MustCompile(`协议类型：([^<\n\r]+?)(?:</p>|<br)`)
		if protocolMatch := protocolRegex.FindStringSubmatch(sectionHTML); len(protocolMatch) > 1 {
			vpn.Protocol = strings.TrimSpace(protocolMatch[1])
		}

		// 提取节点数量
		nodeCountRegex := regexp.MustCompile(`节点数量：([^<\n\r]+?)(?:</p>|<br)`)
		if nodeCountMatch := nodeCountRegex.FindStringSubmatch(sectionHTML); len(nodeCountMatch) > 1 {
			vpn.NodeCount = strings.TrimSpace(nodeCountMatch[1])
		}

		// 提取官方群组（处理"无"的情况）
		groupRegex := regexp.MustCompile(`官方群组：(?:<a[^>]*href="([^"]+)"[^>]*>([^<]+)</a>|([^<\n\r]+?))(?:</p>|<br)`)
		if groupMatch := groupRegex.FindStringSubmatch(sectionHTML); len(groupMatch) > 1 {
			if groupMatch[1] != "" {
				// 有链接的情况
				vpn.OfficialGroup = strings.TrimSpace(groupMatch[1])
			} else if groupMatch[3] != "" && !strings.Contains(groupMatch[3], "无") {
				// 纯文本但不是"无"
				vpn.OfficialGroup = strings.TrimSpace(groupMatch[3])
			}
		}

		// 提取官方频道（处理"无"的情况）
		channelRegex := regexp.MustCompile(`官方频道：(?:<a[^>]*href="([^"]+)"[^>]*>([^<]+)</a>|([^<\n\r]+?))(?:</p>|<br)`)
		if channelMatch := channelRegex.FindStringSubmatch(sectionHTML); len(channelMatch) > 1 {
			var channelInfo string
			if channelMatch[1] != "" {
				// 有链接的情况
				channelInfo = "频道: " + strings.TrimSpace(channelMatch[1])
			} else if channelMatch[3] != "" && !strings.Contains(channelMatch[3], "无") {
				// 纯文本但不是"无"
				channelInfo = "频道: " + strings.TrimSpace(channelMatch[3])
			}

			if channelInfo != "" {
				if vpn.OfficialGroup != "" {
					vpn.OfficialGroup += " | " + channelInfo
				} else {
					vpn.OfficialGroup = channelInfo
				}
			}
		}

		// 提取更新日期
		updatePatterns := []string{
			`更新于：([^<\n\r]+?)(?:</p>|<br)`,
			`<p>更新于：([^<\n\r]+?)</p>`,
		}

		for _, pattern := range updatePatterns {
			updateRegex := regexp.MustCompile(pattern)
			if updateMatch := updateRegex.FindStringSubmatch(sectionHTML); len(updateMatch) > 1 {
				vpn.UpdateDate = strings.TrimSpace(updateMatch[1])
				break
			}
		}

		// 验证必要信息
		if vpn.Name != "" && vpn.RegisterURL != "" {
			vpnList = append(vpnList, vpn)
			fmt.Printf("成功解析机场: %s (%s), 注册地址: %s\n", vpn.Name, vpn.Type, vpn.RegisterURL)
		} else {
			fmt.Printf("机场信息不完整，跳过: %s\n", vpn.Name)
		}
	}

	return vpnList
}

// findNewVPNs 查找新的试用机场
func (c *VPNCrawler) findNewVPNs(currentVPNs []VPNInfo) []VPNInfo {
	var newVPNs []VPNInfo

	for _, current := range currentVPNs {
		isNew := true
		for _, existing := range c.VPNData {
			if existing.Name == current.Name && existing.RegisterURL == current.RegisterURL {
				isNew = false
				break
			}
		}
		if isNew {
			newVPNs = append(newVPNs, current)
		}
	}

	return newVPNs
}

// generateDingTalkSign 生成钉钉签名
func (c *VPNCrawler) generateDingTalkSign() (int64, string) {
	timestamp := time.Now().UnixNano() / 1e6
	stringToSign := fmt.Sprintf("%d\n%s", timestamp, c.NotifyConfig.DingTalkSecret)

	h := hmac.New(sha256.New, []byte(c.NotifyConfig.DingTalkSecret))
	h.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return timestamp, signature
}

// sendDesktopNotification 发送桌面通知
func (c *VPNCrawler) sendDesktopNotification(vpns []VPNInfo) error {
	if len(vpns) == 0 {
		return nil
	}

	title := fmt.Sprintf("发现 %d 个新的免费/试用机场!", len(vpns))

	var message strings.Builder
	for i, vpn := range vpns {
		if i >= 3 { // 桌面通知只显示前3个，避免内容过长
			message.WriteString(fmt.Sprintf("...还有 %d 个机场", len(vpns)-3))
			break
		}

		message.WriteString(fmt.Sprintf("%d. %s (%s)", i+1, vpn.Name, vpn.Type))
		if vpn.CouponCode != "" {
			message.WriteString(fmt.Sprintf(" - %s", vpn.CouponCode))
		}
		if i < len(vpns)-1 && i < 2 {
			message.WriteString("\n")
		}
	}

	// 尝试发送桌面通知，使用多种方式
	var lastErr error

	// 方法1: 使用默认通知
	lastErr = beeep.Notify(title, message.String(), "")
	if lastErr == nil {
		fmt.Printf("成功发送桌面通知，包含 %d 个新机场\n", len(vpns))
		return nil
	}

	// 方法2: 尝试使用Alert（简单弹窗）
	lastErr = beeep.Alert(title, message.String(), "")
	if lastErr == nil {
		fmt.Printf("成功发送桌面弹窗通知，包含 %d 个新机场\n", len(vpns))
		return nil
	}

	// 方法3: 使用Beep（系统提示音）+ 控制台输出
	beeep.Beep(beeep.DefaultFreq, beeep.DefaultDuration)
	fmt.Printf("桌面通知发送失败，已播放系统提示音。发现 %d 个新机场\n", len(vpns))

	// 在控制台显示详细信息作为备选方案
	fmt.Println("\n=== 桌面通知内容 ===")
	fmt.Printf("标题: %s\n", title)
	fmt.Printf("内容: %s\n", message.String())
	fmt.Println("==================")

	// 不返回错误，因为我们已经通过其他方式通知了用户
	return nil
}

// sendDingTalkNotification 发送钉钉通知
func (c *VPNCrawler) sendDingTalkNotification(vpns []VPNInfo) error {
	if len(vpns) == 0 {
		return nil
	}

	// 构建Markdown格式的消息
	var msgBuilder strings.Builder
	msgBuilder.WriteString("# 🎉 发现新的免费/试用机场!\n\n")

	for i, vpn := range vpns {
		msgBuilder.WriteString(fmt.Sprintf("## %d. %s (%s)\n\n", i+1, vpn.Name, vpn.Type))

		if vpn.CouponCode != "" {
			if vpn.Package != "" {
				msgBuilder.WriteString(fmt.Sprintf("**获取方式**: %s (%s)\n\n", vpn.CouponCode, vpn.Package))
			} else {
				msgBuilder.WriteString(fmt.Sprintf("**获取方式**: %s\n\n", vpn.CouponCode))
			}
		}

		if vpn.RegisterURL != "" {
			msgBuilder.WriteString(fmt.Sprintf("**注册地址**: [点击注册](%s)\n\n", vpn.RegisterURL))
		}

		if vpn.Locations != "" {
			msgBuilder.WriteString(fmt.Sprintf("**节点位置**: %s\n\n", vpn.Locations))
		}

		if vpn.Protocol != "" {
			msgBuilder.WriteString(fmt.Sprintf("**协议类型**: %s\n\n", vpn.Protocol))
		}

		if vpn.NodeCount != "" {
			msgBuilder.WriteString(fmt.Sprintf("**节点数量**: %s\n\n", vpn.NodeCount))
		}

		if vpn.OfficialGroup != "" {
			msgBuilder.WriteString(fmt.Sprintf("**官方群组**: [点击加入](%s)\n\n", vpn.OfficialGroup))
		}

		if vpn.UpdateDate != "" {
			msgBuilder.WriteString(fmt.Sprintf("**更新日期**: %s\n\n", vpn.UpdateDate))
		}

		msgBuilder.WriteString("---\n\n")
	}

	message := DingTalkMessage{
		MsgType: "markdown",
		Markdown: DingTalkMarkdown{
			Title: fmt.Sprintf("发现 %d 个新的免费/试用机场", len(vpns)),
			Text:  msgBuilder.String(),
		},
	}

	// 生成签名
	timestamp, sign := c.generateDingTalkSign()

	// 构建完整的URL
	fullURL := fmt.Sprintf("%s&timestamp=%d&sign=%s", c.NotifyConfig.DingTalkURL, timestamp, sign)

	// 序列化消息
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化钉钉消息失败: %v", err)
	}

	// 发送HTTP请求
	resp, err := http.Post(fullURL, "application/json", strings.NewReader(string(jsonData)))
	if err != nil {
		return fmt.Errorf("发送钉钉消息失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("钉钉API返回错误状态码 %d: %s", resp.StatusCode, string(body))
	}

	fmt.Printf("成功发送钉钉通知，包含 %d 个新机场\n", len(vpns))
	return nil
}

// sendFileNotification 发送文件通知
func (c *VPNCrawler) sendFileNotification(vpns []VPNInfo) error {
	if len(vpns) == 0 {
		return nil
	}

	// 生成文件名：格式为 "VPN通知_2024-01-15_14-30-25.txt"
	now := time.Now()
	filename := fmt.Sprintf("VPN通知_%s.txt", now.Format("2006-01-02_15-04-05"))

	// 构建文件内容
	var content strings.Builder
	content.WriteString(fmt.Sprintf("=== 发现 %d 个新的免费/试用机场 ===\n", len(vpns)))
	content.WriteString(fmt.Sprintf("发现时间: %s\n\n", now.Format("2006-01-02 15:04:05")))

	for i, vpn := range vpns {
		content.WriteString(fmt.Sprintf("%d. %s (%s)\n", i+1, vpn.Name, vpn.Type))
		content.WriteString(strings.Repeat("=", 50) + "\n")

		if vpn.CouponCode != "" {
			if vpn.Package != "" {
				content.WriteString(fmt.Sprintf("获取方式: %s (%s)\n", vpn.CouponCode, vpn.Package))
			} else {
				content.WriteString(fmt.Sprintf("获取方式: %s\n", vpn.CouponCode))
			}
		}

		if vpn.RegisterURL != "" {
			content.WriteString(fmt.Sprintf("注册地址: %s\n", vpn.RegisterURL))
		}

		if vpn.Locations != "" {
			content.WriteString(fmt.Sprintf("节点位置: %s\n", vpn.Locations))
		}

		if vpn.Protocol != "" {
			content.WriteString(fmt.Sprintf("协议类型: %s\n", vpn.Protocol))
		}

		if vpn.NodeCount != "" {
			content.WriteString(fmt.Sprintf("节点数量: %s\n", vpn.NodeCount))
		}

		if vpn.OfficialGroup != "" {
			content.WriteString(fmt.Sprintf("官方群组: %s\n", vpn.OfficialGroup))
		}

		if vpn.UpdateDate != "" {
			content.WriteString(fmt.Sprintf("更新日期: %s\n", vpn.UpdateDate))
		}

		content.WriteString("\n")
	}

	content.WriteString("\n=== 使用说明 ===\n")
	content.WriteString("1. 点击注册地址进行注册\n")
	content.WriteString("2. 根据获取方式获取试用套餐\n")
	content.WriteString("3. 如有优惠券代码，请在注册时使用\n")
	content.WriteString("4. 建议优先选择节点较多、协议支持较好的机场\n")

	// 写入文件
	err := os.WriteFile(filename, []byte(content.String()), 0644)
	if err != nil {
		return fmt.Errorf("写入通知文件失败: %v", err)
	}

	fmt.Printf("成功创建通知文件: %s\n", filename)

	// 自动打开文件
	err = c.openFile(filename)
	if err != nil {
		fmt.Printf("自动打开文件失败: %v\n", err)
		fmt.Printf("请手动打开文件: %s\n", filename)
	} else {
		fmt.Printf("已自动打开通知文件\n")
	}

	return nil
}

// openFile 根据操作系统自动打开文件
func (c *VPNCrawler) openFile(filename string) error {
	var cmd *exec.Cmd

	// Windows系统使用start命令
	cmd = exec.Command("cmd", "/c", "start", "", filename)

	return cmd.Start()
}

// sendNotifications 发送通知（支持多种方式）
func (c *VPNCrawler) sendNotifications(vpns []VPNInfo) {
	if len(vpns) == 0 {
		return
	}

	var errors []string

	// 发送钉钉通知
	if c.NotifyConfig.EnableDingTalk {
		if c.NotifyConfig.DingTalkURL == "" || c.NotifyConfig.DingTalkSecret == "" {
			errors = append(errors, "钉钉通知配置不完整")
		} else {
			if err := c.sendDingTalkNotification(vpns); err != nil {
				errors = append(errors, fmt.Sprintf("钉钉通知发送失败: %v", err))
			}
		}
	}

	// 发送桌面通知
	if c.NotifyConfig.EnableDesktop {
		if err := c.sendDesktopNotification(vpns); err != nil {
			errors = append(errors, fmt.Sprintf("桌面通知发送失败: %v", err))
		}
	}

	// 发送文件通知
	if c.NotifyConfig.EnableFile {
		if err := c.sendFileNotification(vpns); err != nil {
			errors = append(errors, fmt.Sprintf("文件通知发送失败: %v", err))
		}
	}

	// 输出错误信息
	if len(errors) > 0 {
		fmt.Println("通知发送过程中出现以下错误:")
		for _, errMsg := range errors {
			fmt.Printf("- %s\n", errMsg)
		}
	}

	// 如果没有启用任何通知方式，给出提示
	if !c.NotifyConfig.EnableDingTalk && !c.NotifyConfig.EnableDesktop && !c.NotifyConfig.EnableFile {
		fmt.Println("提示: 当前未启用任何通知方式，可以在配置中启用钉钉通知、桌面通知或文件通知")
	}
}

// crawlAndCheck 执行爬取和检查
func (c *VPNCrawler) crawlAndCheck() {
	fmt.Printf("\n=== %s 开始爬取试用机场信息 ===\n", time.Now().Format("2006-01-02 15:04:05"))

	// 获取网页内容
	html, err := c.fetchWebPage()
	if err != nil {
		fmt.Printf("获取网页失败: %v\n", err)
		return
	}

	fmt.Printf("网页内容长度: %d 字符\n", len(html))

	// 解析试用机场信息
	currentVPNs := c.parseTrialVPNs(html)
	fmt.Printf("本次爬取到 %d 个试用机场\n", len(currentVPNs))

	// 打印当前爬取到的机场信息
	if len(currentVPNs) > 0 {
		fmt.Println("\n当前爬取到的机场:")
		for i, vpn := range currentVPNs {
			fmt.Printf("%d. %s - %s\n", i+1, vpn.Name, vpn.RegisterURL)
		}
	}

	// 查找新的机场
	newVPNs := c.findNewVPNs(currentVPNs)

	if len(newVPNs) > 0 {
		fmt.Printf("\n🎉 发现 %d 个新的试用机场:\n", len(newVPNs))
		for i, vpn := range newVPNs {
			fmt.Printf("%d. %s\n", i+1, vpn.Name)
		}

		// 发送通知（支持多种方式）
		c.sendNotifications(newVPNs)

		// 更新本地数据
		c.VPNData = append(c.VPNData, newVPNs...)
		if err := c.saveData(); err != nil {
			fmt.Printf("保存数据失败: %v\n", err)
		} else {
			fmt.Println("数据已保存到本地文件")
		}
	} else {
		fmt.Println("没有发现新的试用机场")
	}

	fmt.Printf("=== 本次检查完成，总共存储 %d 个机场 ===\n\n", len(c.VPNData))
}

// run 启动定时任务
func (c *VPNCrawler) run() {
	// 立即执行一次
	c.crawlAndCheck()

	// 每5分钟执行一次
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	fmt.Println("定时任务已启动，每5分钟检查一次...")

	for range ticker.C {
		c.crawlAndCheck()
	}
}

func VPNCrawlerMain() {
	// 配置参数
	dataFile := "vpn_data.json" // 数据存储文件

	// 通知配置 - 用户可以选择启用一种或多种通知方式
	notifyConfig := NotificationConfig{
		EnableDingTalk: false,                                                                                                                // 是否启用钉钉通知
		EnableDesktop:  true,                                                                                                                 // 是否启用桌面通知
		EnableFile:     true,                                                                                                                 // 是否启用文件通知
		DingTalkURL:    "https://oapi.dingtalk.com/robot/send?access_token=7debd389741b51484bf8e10726e397269e9896139496e9cba0f11f06b0cab423", // 钉钉机器人webhook URL
		DingTalkSecret: "SECdfe010745044c45132014d2f13476cf29792396b572429e73901cd4572c3a63d",                                                // 钉钉机器人密钥
	}

	// 输出当前通知配置
	fmt.Println("=== 通知配置 ===")
	if notifyConfig.EnableDingTalk {
		fmt.Println("✓ 钉钉通知: 已启用")
	} else {
		fmt.Println("✗ 钉钉通知: 已禁用")
	}

	if notifyConfig.EnableDesktop {
		fmt.Println("✓ 桌面通知: 已启用")
	} else {
		fmt.Println("✗ 桌面通知: 已禁用")
	}

	if notifyConfig.EnableFile {
		fmt.Println("✓ 文件通知: 已启用")
	} else {
		fmt.Println("✗ 文件通知: 已禁用")
	}
	fmt.Println()

	// 检查钉钉配置（如果启用了钉钉通知）
	if notifyConfig.EnableDingTalk {
		if notifyConfig.DingTalkURL == "YOUR_DINGTALK_WEBHOOK_URL" || notifyConfig.DingTalkSecret == "YOUR_DINGTALK_SECRET" {
			fmt.Println("警告: 钉钉通知已启用但配置不完整！")
			fmt.Println("请在main函数中修改以下变量：")
			fmt.Println("- DingTalkURL: 钉钉机器人的 webhook URL")
			fmt.Println("- DingTalkSecret: 钉钉机器人的加签密钥")
			fmt.Println("或者将 EnableDingTalk 设置为 false 禁用钉钉通知")
			fmt.Println()
		}
	}

	// 创建爬虫实例
	crawler := NewVPNCrawler(dataFile, notifyConfig)

	// 启动爬虫
	crawler.run()
}
